// UI Components - Basic building blocks for the application
// These components should be highly reusable and follow design system principles

// Buttons
export { default as Button } from './Button/Button';
export type { ButtonProps } from './Button/Button';

// Inputs
export { default as Input } from './Input/Input';
export type { InputProps } from './Input/Input';

// Display
export { default as Card } from './Card/Card';
export type { CardProps } from './Card/Card';

// Dashboard Components
export { default as NotificationBar } from './NotificationBar/NotificationBar';
export type { NotificationBarProps } from './NotificationBar/NotificationBar';

export { default as AppTile } from './AppTile/AppTile';
export type { AppTileProps } from './AppTile/AppTile';

export { default as SearchOverlay } from './SearchOverlay/SearchOverlay';
export type { SearchOverlayProps } from './SearchOverlay/SearchOverlay';

export { default as TopNavigation } from './TopNavigation/TopNavigation';
export type { TopNavigationProps } from './TopNavigation/TopNavigation';

// Typography
export { default as Text } from './Text/Text';
export type { TextProps } from './Text/Text';
export { default as Heading } from './Heading/Heading';
export type { HeadingProps } from './Heading/Heading';

// Layout
export { default as Separator } from './Separator/Separator';
export type { SeparatorProps } from './Separator/Separator';

// TODO: Add more components as they are implemented
// export { default as TextArea } from './TextArea/TextArea'
// export { default as Select } from './Select/Select'
// export { default as Checkbox } from './Checkbox/Checkbox'
// export { default as Radio } from './Radio/Radio'
// export { default as Badge } from './Badge/Badge'
// export { default as Avatar } from './Avatar/Avatar'
// export { default as Divider } from './Divider/Divider'
// export { default as Spinner } from './Spinner/Spinner'
// export { default as Skeleton } from './Skeleton/Skeleton'
// export { default as Icon } from './Icon/Icon'
