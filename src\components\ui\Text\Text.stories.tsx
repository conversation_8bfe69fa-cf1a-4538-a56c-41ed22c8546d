import type { Meta, StoryObj } from '@storybook/react-vite';
import Text from './Text';

const meta: Meta<typeof Text> = {
  title: 'UI/Typography/Text',
  component: Text,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A versatile text component with multiple variants, sizes, and theme-aware styling. Provides consistent typography across the application.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['body', 'caption', 'overline', 'subtitle1', 'subtitle2'],
      description: 'Text variant with predefined styling',
    },
    size: {
      control: { type: 'select' },
      options: ['xs', 'sm', 'base', 'lg', 'xl'],
      description: 'Text size override',
    },
    weight: {
      control: { type: 'select' },
      options: ['normal', 'medium', 'semibold', 'bold'],
      description: 'Font weight',
    },
    color: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'muted', 'error', 'warning', 'success', 'info', 'inherit'],
      description: 'Text color variant',
    },
    align: {
      control: { type: 'select' },
      options: ['left', 'center', 'right', 'justify'],
      description: 'Text alignment',
    },
    transform: {
      control: { type: 'select' },
      options: ['none', 'uppercase', 'lowercase', 'capitalize'],
      description: 'Text transformation',
    },
    as: {
      control: { type: 'select' },
      options: ['p', 'span', 'div', 'label', 'small'],
      description: 'HTML element to render',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'This is default body text',
  },
};

export const Variants: Story = {
  render: () => (
    <div className="space-y-4">
      <Text variant="subtitle1">Subtitle 1 - Large and medium weight</Text>
      <Text variant="subtitle2">Subtitle 2 - Base size and medium weight</Text>
      <Text variant="body">Body text - Standard paragraph text</Text>
      <Text variant="caption">Caption text - Smaller descriptive text</Text>
      <Text variant="overline" transform="uppercase">Overline text - Small caps with tracking</Text>
    </div>
  ),
};

export const Colors: Story = {
  render: () => (
    <div className="space-y-2">
      <Text color="inherit">Inherit color (default)</Text>
      <Text color="primary">Primary color text</Text>
      <Text color="secondary">Secondary color text</Text>
      <Text color="muted">Muted color text</Text>
      <Text color="error">Error color text</Text>
      <Text color="warning">Warning color text</Text>
      <Text color="success">Success color text</Text>
      <Text color="info">Info color text</Text>
    </div>
  ),
};

export const Sizes: Story = {
  render: () => (
    <div className="space-y-2">
      <Text size="xs">Extra small text</Text>
      <Text size="sm">Small text</Text>
      <Text size="base">Base text</Text>
      <Text size="lg">Large text</Text>
      <Text size="xl">Extra large text</Text>
    </div>
  ),
};

export const Weights: Story = {
  render: () => (
    <div className="space-y-2">
      <Text weight="normal">Normal weight</Text>
      <Text weight="medium">Medium weight</Text>
      <Text weight="semibold">Semibold weight</Text>
      <Text weight="bold">Bold weight</Text>
    </div>
  ),
};

export const Truncation: Story = {
  render: () => (
    <div className="w-48 space-y-4">
      <div>
        <Text variant="caption" color="muted">Truncated text:</Text>
        <Text truncate>This is a very long text that will be truncated with ellipsis</Text>
      </div>
      <div>
        <Text variant="caption" color="muted">Line clamped (2 lines):</Text>
        <Text lineClamp={2}>
          This is a longer text that will be clamped to exactly two lines and show ellipsis at the end when it overflows beyond the specified number of lines.
        </Text>
      </div>
    </div>
  ),
};
