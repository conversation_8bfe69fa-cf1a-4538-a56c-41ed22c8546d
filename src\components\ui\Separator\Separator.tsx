import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';

export interface SeparatorProps {
  variant?: 'line' | 'dots' | 'wave' | 'gradient' | 'ornamental' | 'geometric';
  orientation?: 'horizontal' | 'vertical';
  size?: 'sm' | 'md' | 'lg';
  spacing?: 'sm' | 'md' | 'lg' | 'xl';
  opacity?: number;
  className?: string;
  'data-testid'?: string;
}

const Separator: React.FC<SeparatorProps> = ({
  variant = 'line',
  orientation = 'horizontal',
  size = 'md',
  spacing = 'md',
  opacity = 0.2,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const spacingClasses = {
    sm: orientation === 'horizontal' ? 'my-2' : 'mx-2',
    md: orientation === 'horizontal' ? 'my-4' : 'mx-4',
    lg: orientation === 'horizontal' ? 'my-6' : 'mx-6',
    xl: orientation === 'horizontal' ? 'my-8' : 'mx-8',
  };

  const sizeClasses = {
    sm: orientation === 'horizontal' ? 'h-px' : 'w-px',
    md: orientation === 'horizontal' ? 'h-0.5' : 'w-0.5',
    lg: orientation === 'horizontal' ? 'h-1' : 'w-1',
  };

  const baseClasses = cn(
    'flex items-center justify-center',
    spacingClasses[spacing],
    className
  );

  const separatorColor = `${colors.border}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`;

  const renderLine = () => (
    <div
      className={cn(
        'flex-1',
        sizeClasses[size],
        orientation === 'vertical' && 'h-full'
      )}
      style={{ backgroundColor: separatorColor }}
    />
  );

  const renderDots = () => {
    const dotCount = size === 'sm' ? 3 : size === 'md' ? 5 : 7;
    const dotSize = size === 'sm' ? 'w-1 h-1' : size === 'md' ? 'w-1.5 h-1.5' : 'w-2 h-2';
    
    return (
      <div className={cn(
        'flex items-center',
        orientation === 'horizontal' ? 'space-x-2' : 'flex-col space-y-2'
      )}>
        {Array.from({ length: dotCount }).map((_, i) => (
          <div
            key={i}
            className={cn('rounded-full', dotSize)}
            style={{ backgroundColor: separatorColor }}
          />
        ))}
      </div>
    );
  };

  const renderWave = () => (
    <svg
      className={cn(
        orientation === 'horizontal' ? 'w-full h-4' : 'h-full w-4'
      )}
      viewBox="0 0 100 20"
      preserveAspectRatio="none"
    >
      <path
        d="M0,10 Q25,0 50,10 T100,10"
        stroke={separatorColor}
        strokeWidth="2"
        fill="none"
        vectorEffect="non-scaling-stroke"
      />
    </svg>
  );

  const renderGradient = () => (
    <div
      className={cn(
        'flex-1',
        sizeClasses[size],
        orientation === 'vertical' && 'h-full'
      )}
      style={{
        background: orientation === 'horizontal'
          ? `linear-gradient(to right, transparent, ${separatorColor}, transparent)`
          : `linear-gradient(to bottom, transparent, ${separatorColor}, transparent)`
      }}
    />
  );

  const renderOrnamental = () => (
    <div className="flex items-center space-x-2">
      <div
        className="w-8 h-px"
        style={{ backgroundColor: separatorColor }}
      />
      <svg
        className="w-4 h-4"
        viewBox="0 0 24 24"
        fill="none"
      >
        <path
          d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"
          fill={separatorColor}
        />
      </svg>
      <div
        className="w-8 h-px"
        style={{ backgroundColor: separatorColor }}
      />
    </div>
  );

  const renderGeometric = () => (
    <div className="flex items-center space-x-1">
      {Array.from({ length: 3 }).map((_, i) => (
        <div key={i} className="flex items-center space-x-1">
          <div
            className="w-2 h-2 rotate-45"
            style={{ backgroundColor: separatorColor }}
          />
          {i < 2 && (
            <div
              className="w-4 h-px"
              style={{ backgroundColor: separatorColor }}
            />
          )}
        </div>
      ))}
    </div>
  );

  const renderSeparator = () => {
    switch (variant) {
      case 'dots':
        return renderDots();
      case 'wave':
        return renderWave();
      case 'gradient':
        return renderGradient();
      case 'ornamental':
        return renderOrnamental();
      case 'geometric':
        return renderGeometric();
      case 'line':
      default:
        return renderLine();
    }
  };

  return (
    <div className={baseClasses} data-testid={testId}>
      {renderSeparator()}
    </div>
  );
};

export default Separator;
